class OrderProduct {
  OrderProduct(
      {required this.itemId,
      required this.itemName,
      required this.itemDescription,
      required this.itemPrice,
      required this.itemQuantity,
      required this.itemImage,
      required this.itemImageThumb,
      this.itemTotalPrice,
      this.itemCurrencyVar});

  int itemId;
  String itemName;
  String itemDescription;
  double itemPrice;
  int itemQuantity;
  String itemImage;
  String itemImageThumb;
  double? itemTotalPrice;

  String? itemCurrencyVar;
  // from json
  factory OrderProduct.fromJson(Map<String, dynamic> json) {
    return OrderProduct(
        itemId: json['item_id'] as int,
        itemName: json['item_name'] as String,
        itemDescription: json['item_description'] as String,
        itemPrice: double.parse(json['item_price'].toString()),
        itemQuantity: json['item_quantity'] as int,
        itemImage: json['item_image'] as String,
        itemImageThumb: json['item_image_thumb'] as String,
        itemCurrencyVar: json['item_CurrencyVar'] as String?,
        itemTotalPrice: double.tryParse(json['item_total_price'].toString()));
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'item_id': itemId,
      'item_name': itemName,
      'item_description': itemDescription,
      'item_price': itemPrice,
      'item_quantity': itemQuantity,
      'item_image': itemImage,
      'item_image_thumb': itemImageThumb,
      'item_CurrencyVar': itemCurrencyVar
    };
  }
}
