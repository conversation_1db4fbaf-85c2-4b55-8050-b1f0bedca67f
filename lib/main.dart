import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:matjari_driver/firebase_options.dart';

import 'src/app.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  //init firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
//init hive
  await Hive.initFlutter();

  runApp(const MatjariDriverApp());
}
