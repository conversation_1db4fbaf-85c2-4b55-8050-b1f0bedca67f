import 'dart:async';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';

class CustomRetryInterceptor extends RetryInterceptor {
  CustomRetryInterceptor(
      {required super.dio,
      this.customonError,
      super.retries,
      super.retryableExtraStatuses});

  Function(DioException)? customonError;
  @override
  Future<dynamic> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    var error = err;
    if (customonError != null) {
      error = customonError!(err);
    }

    super.onError(error, handler);
  }
}
